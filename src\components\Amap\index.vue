<template>
  <div class="map-container">
    <div id="map" class="map"></div>
  </div>
</template>

<script>
import mapMixin from '@/views/flightmaster/mixins/mapMixin.js'
export default {
    mixins: [ mapMixin],
  name: 'Amap',
  props: {
    coordinates: {
      type: Array,
      default: () => []
    },
    zoom: {
      type: Number,
      default: 13
    }
  },
  data() {
    return {
      map: null
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      const that = this;
      const wgs84Coord = this.gcj02ToWgs84(113.014701, 28.194985);
      that.map = new AMap.Map('map', {
        center: that.coordinates[0] || [wgs84Coord.lng, wgs84Coord.lat],
        zoom: that.zoom,
        viewMode: '2D'  // 设置地图模式
      });
  

      // 使用高德地图的定位插件
      AMap.plugin('AMap.Geolocation', function() {
        const geolocation = new AMap.Geolocation({
          enableHighAccuracy: true, // 是否使用高精度定位，默认：true
          timeout: 10000,           // 超过10秒后停止定位，默认：无穷大
          maximumAge: 0,            // 定位结果缓存0毫秒，默认：0
          convert: true,            // 自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showMarker: true,         // 定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true,         // 定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true,      // 定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true      // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        });
  
        that.map.addControl(geolocation);
        geolocation.getCurrentPosition((status, result) => {
        
          if (status === 'complete') {
            console.log('定位成功', result);
          } else {
            console.error('定位失败', result);
          }
        });
      });

      // 添加自定义标记和圆形
      that.coordinates.forEach(coord => {
        // 添加标记
        // new AMap.Marker({
        //   position: coord,
        //   map: that.map,
        //   icon: new AMap.Icon({
        //     size: new AMap.Size(40, 40),
        //     image: require('@/assets/images/wrj_jc.png'),
        //     imageSize: new AMap.Size(40, 40)
        //   }),
        //   offset: new AMap.Pixel(-20, -20),
        //   title: '位置'
        // });

        // // 添加圆形
        // const circle = new AMap.Circle({
        //   center: new AMap.LngLat(coord[0], coord[1]),  // 确保使用经纬度对象
        //   radius: 1000,  // 1000米 = 1公里
        //   fillColor: 'rgba(173, 216, 230, 0.3)',  // 浅蓝色 (LightBlue)
        //   fillOpacity: 0.3,  // 填充透明度
        //   strokeColor: '#1E90FF',  // 道奇蓝，更深的蓝色
        //   strokeOpacity: 0.8,  // 提高边框透明度
        //   strokeWeight: 2,  // 增加边框宽度
        //   strokeStyle: 'solid',
        //   zIndex: 50,
        // });
        
        // // 将圆形添加到地图
        // circle.setMap(that.map);
        
      });
    }
  }
}
</script>

<style>
.map-container {
  width: 100vw; /* 设置宽度为视口宽度 */
  height: 100vh; /* 设置高度为视口高度 */
  overflow: hidden;
  position1: fixed; /* 固定位置 */
  top: 0;
  left: 0;
}

#map {
  width: 100%;
  height: 100%;
}
</style>