/**
 * 地图处理Mixin
 * 提供地图相关功能，包括坐标转换、区域绘制、城市名称获取等
 */
/* global AMap */
import { rectangle } from '@/api/system/dict/data';
import axios from 'axios';

// 常量定义
const PI = 3.14159265358979324;
const A = 6378245.0;
const EE = 0.00669342162296594323;

export default {
  data() {
    return {
      coordinates: [
        // [113.091931, 28.264330],
        // [113.092931, 28.265330],
        // 添加更多坐标
      ],
      cityNames: [], // 新增属性，用于存储城市名称
      infoWindow: null, // 用于存储信息窗体实例
      selectedRegion: { name: '' }
    };
  },

  methods: {
    // 新增方法：通过经纬度获取城市名称
    async fetchCityNames() {
      // 从设备列表中提取坐标信息
      const deviceCoordinates = this.deviceList
        .filter(device => device.longitude && device.latitude)
        .map(device => [device.longitude, device.latitude]);

      console.log('设备坐标列表:', deviceCoordinates);

      if (deviceCoordinates.length === 0) {
        console.warn('没有找到有效的设备坐标');
        return;
      }

      const promises = deviceCoordinates.map(async (coord) => {
        const lng = coord[0];
        const lat = coord[1];
        try {
          const response = await axios.get(`https://res.abeim.cn/api-location_geocoder_address?lng=${lng}&lat=${lat}`);
          if (response.data.code === 200) {
            return response.data.data.city; // 返回城市名称
          }
        } catch (error) {
          console.error('获取城市名称失败:', error);
          return null; // 处理错误
        }
      });

      // 等待所有请求完成
      this.cityNames = [...new Set(await Promise.all(promises))].filter(city => city); // 使用 Set 去重并过滤空值
      console.log('城市名称:', this.cityNames); // 调试用

      // 为每个城市获取禁飞区数据
      for (const city of this.cityNames) {
        try {
          const response = await rectangle(city);
          console.log(`${city}的禁飞区数据:`, response);
          if (response && response.data && response.data.areas) {
            this.drawRegions(response, this.$refs.amap.map);
          }
        } catch (error) {
          console.error(`获取${city}禁飞区数据失败:`, error);
        }
      }
    },

   

    // 创建自定义信息窗体内容
    createInfoWindowContent(name, level, color, height) {
      console.log("创建信息窗体内容", name, level, color, height);
      // 创建一个包含区域信息的自定义内容
      const content = document.createElement('div');
      content.className = 'custom-info-window';

      // 添加区域名称
      const areaTitle = document.createElement('div');
      areaTitle.className = 'info-area-title';
      areaTitle.innerHTML = `<span class="info-label">区域：</span>${name || '未知区域'}`;
      content.appendChild(areaTitle);

      // 根据颜色确定区域类型和样式类
      let areaType = '未知区域';
      let styleClass = 'warning-level';

      if (color === '#DE4329') {
        areaType = '禁飞区';
        styleClass = 'no-fly-zone';
      } else if (color === '#979797') {
        areaType = '限高区';
        styleClass = 'height-restricted-zone';
      } else if (color === '#EE8815') {
        areaType = '加强警示区';
        styleClass = 'warning-zone';
      }

      // 添加等级信息
      const levelInfo = document.createElement('div');
      levelInfo.className = 'info-level';
      levelInfo.innerHTML = `<span class="info-label">等级：</span><span class="${styleClass}">${areaType}</span>`;
      content.appendChild(levelInfo);

      // 如果是限高区，添加限高信息
      if (color === '#979797' && height) {
        const heightInfo = document.createElement('div');
        heightInfo.className = 'info-height';
        heightInfo.innerHTML = `<span class="info-label">限高：</span><span class="height-value">${height}米</span>`;
        content.appendChild(heightInfo);
      }

      return content;
    },

    // 绘制圆形区域和多边形区域的函数
    drawRegions(regions, map) {
      // 首先按面积大小对区域进行排序
      const sortedAreas = [...regions.data.areas].sort((a, b) => {
        // 对于圆形区域，比较半径
        if (a.shape === 0 && b.shape === 0) {
          return b.radius - a.radius; // 大区域在前
        }
        // 对于多边形区域，可以根据点的数量或其他属性排序
        return 0;
      });

      // 按照从大到小的顺序绘制区域，小区域的 zIndex 更高
      sortedAreas.forEach((region2, index) => {
        // 计算 zIndex，小区域有更高的 zIndex
        const baseZIndex = 50;
        const zIndexValue = baseZIndex + index * 10; // 每个区域增加10的zIndex

        if (region2.shape === 0) {
          const gcj02Coord = this.wgs84ToGcj02(region2.lng, region2.lat);
          region2.lng = gcj02Coord.lng;
          region2.lat = gcj02Coord.lat;
          const circle = new AMap.Circle({
            center: [region2.lng, region2.lat],
            radius: region2.radius,
            strokeColor: region2.color,
            strokeWeight: 2,
            fillColor: region2.color,
            fillOpacity: 0.35,
            zIndex: zIndexValue, // 使用计算的 zIndex
            cursor: 'pointer',
            extData: { // 存储区域信息，用于点击事件
              name: region2.name,
              level: region2.level,
              color: region2.color,
              height: region2.height
            }
          });

          // 添加点击事件
          circle.on('click', (e) => {
            const extData = e.target.getExtData();
            console.log("点击了圆形区域:", extData);

            // 创建信息窗体内容
            const content = this.createInfoWindowContent(
              extData.name,
              extData.level,
              extData.color,
              extData.height
            );

            // 设置信息窗体内容并打开
            this.infoWindow.setContent(content);
            this.infoWindow.open(map, e.lnglat);
          });

          map.add(circle);
        } else if (region2.shape === 1) {
          // 多边形区域
          region2.polygon_points.forEach((polygon, polygonIndex) => {
            // 转换坐标系
            const convertedPolygon = polygon.map(point => {
              const gcj02Coord = this.wgs84ToGcj02(point[0], point[1]);
              return [gcj02Coord.lng, gcj02Coord.lat];
            });

            // 检查多边形是否有效（至少3个点）
            if (convertedPolygon.length >= 3) {
              const polygon3 = new AMap.Polygon({
                path: convertedPolygon,
                strokeColor: region2.color,
                strokeWeight: 1,
                fillColor: region2.color,
                fillOpacity: 0.35,
                zIndex: zIndexValue + polygonIndex, // 每个多边形有不同的zIndex
                cursor: 'pointer',
                extData: { // 存储区域信息，用于点击事件
                  name: region2.name,
                  level: region2.level,
                  color: region2.color,
                  height: region2.height
                }
              });

              // 添加点击事件
              polygon3.on('click', (e) => {
                const extData = e.target.getExtData();
                console.log("点击了多边形区域:", extData);

                // 创建信息窗体内容
                const content = this.createInfoWindowContent(
                  extData.name,
                  extData.level,
                  extData.color,
                  extData.height
                );

                // 设置信息窗体内容并打开
                this.infoWindow.setContent(content);
                this.infoWindow.open(map, e.lnglat);
              });

              map.add(polygon3);
            } else {
              console.warn(`多边形 ${region2.name} 的第 ${polygonIndex + 1} 个多边形点数不足，跳过绘制`);
            }
          });
        }
      });
    },




 // 判断是否在中国境内
    outOfChina(lng, lat) {
      if (lng < 72.004 || lng > 137.8347) {
        return true;
      }
      if (lat < 0.8293 || lat > 55.8271) {
        return true;
      }
      return false;
    },

    // 转换经纬度
    transformLat(x, y) {
      let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
      ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
      return ret;
    },

    transformLon(x, y) {
      let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
      ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
      return ret;
    },

    // WGS-84到GCJ-02坐标转换
    wgs84ToGcj02(lng, lat) {
      if (this.outOfChina(lng, lat)) {
        return { lng, lat };
      }
      let dLat = this.transformLat(lng - 105.0, lat - 35.0);
      let dLon = this.transformLon(lng - 105.0, lat - 35.0);
      let radLat = lat / 180.0 * PI;
      let magic = Math.sin(radLat);
      magic = 1 - EE * magic * magic;
      let sqrtMagic = Math.sqrt(magic);
      dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
      dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
      let mgLat = lat + dLat;
      let mgLng = lng + dLon;
      return { lng: mgLng, lat: mgLat };
    },
 // GCJ02 转 WGS84 坐标系转换
    gcj02ToWgs84(lng, lat) {
      if (this.outOfChina(lng, lat)) {
        return { lng, lat };
      }
      let dLat = this.transformLat(lng - 105.0, lat - 35.0);
      let dLon = this.transformLon(lng - 105.0, lat - 35.0);
      const PI = 3.14159265358979324;
      const A = 6378245.0;
      const EE = 0.00669342162296594323;
      let radLat = lat / 180.0 * PI;
      let magic = Math.sin(radLat);
      magic = 1 - EE * magic * magic;
      let sqrtMagic = Math.sqrt(magic);
      dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
      dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
      let mgLat = lat - dLat;
      let mgLng = lng - dLon;
      return { lng: mgLng, lat: mgLat };
    },



  }
};
